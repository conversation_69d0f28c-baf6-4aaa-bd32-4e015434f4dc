{"id": "ava-chat-system-environment", "name": "Ava Chat System - Local Development", "values": [{"key": "base_url", "value": "http://localhost:3000", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "type": "secret", "enabled": true}, {"key": "support_access_token", "value": "", "type": "secret", "enabled": true}, {"key": "support_refresh_token", "value": "", "type": "secret", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "support_user_id", "value": "", "type": "default", "enabled": true}, {"key": "conversation_id", "value": "", "type": "default", "enabled": true}, {"key": "jira_ticket_id", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-09-21T04:50:00.000Z", "_postman_exported_using": "Postman/10.0.0"}
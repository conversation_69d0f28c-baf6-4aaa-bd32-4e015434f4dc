# Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Ava Chat System Backend securely in production environments.

## Pre-Deployment Checklist

### 1. Environment Setup

- [ ] Production server provisioned with adequate resources
- [ ] Node.js 18+ installed
- [ ] PM2 or similar process manager installed
- [ ] Nginx or Apache configured as reverse proxy
- [ ] SSL certificates obtained and configured
- [ ] Firewall rules configured
- [ ] Database (Supabase) project created and configured

### 2. Security Requirements

- [ ] Strong JWT secret generated (32+ characters)
- [ ] All API keys obtained and validated
- [ ] Environment variables file created and secured
- [ ] File upload directory permissions set correctly
- [ ] Log rotation configured
- [ ] Monitoring and alerting set up

## Step-by-Step Deployment

### 1. Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Create application user
sudo useradd -m -s /bin/bash ava-chat
sudo usermod -aG sudo ava-chat

# Create application directory
sudo mkdir -p /opt/ava-chat
sudo chown ava-chat:ava-chat /opt/ava-chat
```

### 2. Application Deployment

```bash
# Switch to application user
sudo su - ava-chat

# Clone repository (or upload files)
cd /opt/ava-chat
git clone <your-repository-url> .

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Create uploads directory
mkdir -p uploads
chmod 755 uploads
```

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit environment file with production values
nano .env
```

**Required Environment Variables**:
```bash
NODE_ENV=production
PORT=3000
FRONTEND_URL=https://yourdomain.com
JWT_SECRET=<32+ character secure random string>
SUPABASE_URL=<your-production-supabase-url>
SUPABASE_ANON_KEY=<your-production-anon-key>
SUPABASE_SERVICE_KEY=<your-production-service-key>
```

**Security Environment File Permissions**:
```bash
chmod 600 .env
chown ava-chat:ava-chat .env
```

### 4. Process Management with PM2

Create PM2 ecosystem file:

```bash
# Create ecosystem.config.js
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'ava-chat-backend',
    script: './dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
}
EOF

# Create logs directory
mkdir -p logs

# Start application
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
```

### 5. Nginx Configuration

```bash
# Install Nginx
sudo apt install nginx -y

# Create Nginx configuration
sudo cat > /etc/nginx/sites-available/ava-chat << 'EOF'
upstream ava_chat_backend {
    server 127.0.0.1:3000;
    keepalive 32;
}

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=1r/s;

    # File Upload Limits
    client_max_body_size 10M;

    # Proxy Configuration
    location / {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://ava_chat_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Auth endpoints with stricter rate limiting
    location /api/auth {
        limit_req zone=auth burst=5 nodelay;
        
        proxy_pass http://ava_chat_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check endpoint (no rate limiting)
    location /health {
        proxy_pass http://ava_chat_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        access_log off;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/ava-chat /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 6. Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 7. Log Management

```bash
# Configure log rotation
sudo cat > /etc/logrotate.d/ava-chat << 'EOF'
/opt/ava-chat/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 ava-chat ava-chat
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

## Post-Deployment Verification

### 1. Health Checks

```bash
# Check application status
pm2 status

# Check application logs
pm2 logs ava-chat-backend

# Test health endpoint
curl https://yourdomain.com/health

# Test API endpoint
curl -X POST https://yourdomain.com/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123","name":"Test User","role":"user"}'
```

### 2. Security Verification

```bash
# Check SSL configuration
curl -I https://yourdomain.com

# Verify security headers
curl -I https://yourdomain.com | grep -E "(X-Frame-Options|X-Content-Type-Options|X-XSS-Protection)"

# Test rate limiting
for i in {1..15}; do curl -s -o /dev/null -w "%{http_code}\n" https://yourdomain.com/api/auth/login; done
```

### 3. Performance Testing

```bash
# Install Apache Bench
sudo apt install apache2-utils -y

# Test concurrent requests
ab -n 1000 -c 10 https://yourdomain.com/health

# Monitor system resources
htop
```

## Monitoring and Maintenance

### 1. Application Monitoring

```bash
# Monitor PM2 processes
pm2 monit

# Check memory usage
pm2 show ava-chat-backend

# View real-time logs
pm2 logs ava-chat-backend --lines 100
```

### 2. System Monitoring

Set up monitoring for:
- CPU usage
- Memory usage
- Disk space
- Network traffic
- Application response times
- Error rates

### 3. Backup Strategy

```bash
# Create backup script
cat > /opt/ava-chat/backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups/ava-chat"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup application files
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /opt/ava-chat --exclude=node_modules --exclude=logs

# Backup environment file
cp /opt/ava-chat/.env $BACKUP_DIR/env_$DATE.backup

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
find $BACKUP_DIR -name "*.backup" -mtime +30 -delete
EOF

chmod +x /opt/ava-chat/backup.sh

# Add to crontab for daily backups
echo "0 2 * * * /opt/ava-chat/backup.sh" | crontab -
```

## Troubleshooting

### Common Issues

1. **Application won't start**:
   - Check environment variables
   - Verify database connectivity
   - Check file permissions
   - Review PM2 logs

2. **High memory usage**:
   - Monitor for memory leaks
   - Adjust PM2 max_memory_restart
   - Check for large file uploads

3. **SSL certificate issues**:
   - Verify certificate paths
   - Check certificate expiration
   - Test with SSL Labs

4. **Rate limiting too aggressive**:
   - Adjust Nginx rate limits
   - Monitor legitimate traffic patterns
   - Whitelist known good IPs

### Emergency Procedures

1. **Application crash**:
   ```bash
   pm2 restart ava-chat-backend
   pm2 logs ava-chat-backend --lines 50
   ```

2. **High load**:
   ```bash
   pm2 scale ava-chat-backend +2
   ```

3. **Security incident**:
   ```bash
   # Block suspicious IP
   sudo ufw deny from <suspicious-ip>
   
   # Check access logs
   sudo tail -f /var/log/nginx/access.log
   ```

## Updates and Maintenance

### Application Updates

```bash
# Backup current version
cp -r /opt/ava-chat /opt/ava-chat.backup

# Pull latest code
git pull origin main

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Restart application
pm2 restart ava-chat-backend

# Verify deployment
curl https://yourdomain.com/health
```

### Security Updates

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Node.js dependencies
npm audit fix

# Update PM2
sudo npm update -g pm2

# Restart services
sudo systemctl restart nginx
pm2 restart all
```

---

**Last Updated**: [Current Date]
**Version**: 1.0

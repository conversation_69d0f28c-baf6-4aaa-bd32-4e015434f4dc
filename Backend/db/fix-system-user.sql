-- Fix for system user ID issue
-- This allows the system UUID to be used for AI responses

-- First, let's create a system user profile if it doesn't exist
INSERT INTO profiles (id, email, role, name, avatar_url)
VALUES (
  '00000000-0000-0000-0000-000000000000',
  '<EMAIL>',
  'user',
  'AI Assistant',
  NULL
) ON CONFLICT (id) DO NOTHING;

-- Update the messages table to allow the system user ID
-- (This should already work since we're using a valid UUID format)

-- Optional: Create an index for better performance on sender_id
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);

#!/bin/bash

# =====================================================
# Ava Chat System API Testing Script
# Tests all endpoints from the Postman collection
# =====================================================

BASE_URL="http://localhost:3000"
echo "🧪 Testing Ava Chat System API Endpoints"
echo "Base URL: $BASE_URL"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local data=$4
    local headers=$5
    
    echo -e "\n${BLUE}Testing:${NC} $description"
    echo -e "${YELLOW}$method $endpoint${NC}"
    
    if [ -n "$data" ]; then
        if [ -n "$headers" ]; then
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Content-Type: application/json" \
                -H "$headers" \
                -d "$data")
        else
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Content-Type: application/json" \
                -d "$data")
        fi
    else
        if [ -n "$headers" ]; then
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "$headers")
        else
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint")
        fi
    fi
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        echo -e "${GREEN}✅ Success ($http_code)${NC}"
    elif [ "$http_code" -ge 400 ] && [ "$http_code" -lt 500 ]; then
        echo -e "${YELLOW}⚠️  Client Error ($http_code)${NC}"
    else
        echo -e "${RED}❌ Error ($http_code)${NC}"
    fi
    
    # Show response (truncated)
    echo "Response: $(echo "$response_body" | head -c 200)..."
    
    return $http_code
}

# Test 1: Health Check
test_endpoint "GET" "/health" "Health Check"

# Test 2: API Test Endpoint
test_endpoint "GET" "/api/test" "API Test Endpoint"

# Test 3: Register User (should work)
USER_DATA='{
  "email": "<EMAIL>",
  "password": "Password123!",
  "name": "Test User",
  "role": "user"
}'
test_endpoint "POST" "/api/auth/register" "Register User" "$USER_DATA"

# Test 4: Register Support Agent
SUPPORT_DATA='{
  "email": "<EMAIL>", 
  "password": "Password123!",
  "name": "Test Support Agent",
  "role": "support"
}'
test_endpoint "POST" "/api/auth/register" "Register Support Agent" "$SUPPORT_DATA"

# Test 5: Login User
LOGIN_DATA='{
  "email": "<EMAIL>",
  "password": "Password123!"
}'
echo -e "\n${BLUE}Testing:${NC} Login User"
login_response=$(curl -s -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -d "$LOGIN_DATA")

echo "Login Response: $login_response"

# Extract access token if login successful
ACCESS_TOKEN=$(echo "$login_response" | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)

if [ -n "$ACCESS_TOKEN" ]; then
    echo -e "${GREEN}✅ Login successful, token extracted${NC}"
    
    # Test 6: Get Profile (authenticated)
    test_endpoint "GET" "/api/auth/profile" "Get User Profile" "" "Authorization: Bearer $ACCESS_TOKEN"
    
    # Test 7: Send Chat Message (this might trigger the email error)
    CHAT_DATA='{
      "message": "Hello, I need help with my account settings."
    }'
    test_endpoint "POST" "/api/chat/send-message" "Send Chat Message" "$CHAT_DATA" "Authorization: Bearer $ACCESS_TOKEN"
    
else
    echo -e "${RED}❌ Login failed, cannot test authenticated endpoints${NC}"
fi

# Test 8: Login Support Agent
SUPPORT_LOGIN_DATA='{
  "email": "<EMAIL>",
  "password": "Password123!"
}'
echo -e "\n${BLUE}Testing:${NC} Login Support Agent"
support_login_response=$(curl -s -X POST "$BASE_URL/api/auth/login" \
    -H "Content-Type: application/json" \
    -d "$SUPPORT_LOGIN_DATA")

echo "Support Login Response: $support_login_response"

# Extract support access token
SUPPORT_TOKEN=$(echo "$support_login_response" | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)

if [ -n "$SUPPORT_TOKEN" ]; then
    echo -e "${GREEN}✅ Support login successful${NC}"
    
    # Test 9: Get Support Conversations
    test_endpoint "GET" "/api/support/conversations?status=open" "Get Open Conversations" "" "Authorization: Bearer $SUPPORT_TOKEN"
    
else
    echo -e "${RED}❌ Support login failed${NC}"
fi

echo -e "\n${BLUE}========================================"
echo -e "🎯 API Testing Complete!"
echo -e "========================================${NC}"

echo -e "\n${YELLOW}📋 Next Steps:${NC}"
echo "1. Run the SQL fix in Supabase: SUPABASE_FIX.sql"
echo "2. Import Postman collection: Ava-Chat-System.postman_collection.json"
echo "3. Import Postman environment: Ava-Chat-System.postman_environment.json"
echo "4. Follow the guide in: POSTMAN_SETUP.md"

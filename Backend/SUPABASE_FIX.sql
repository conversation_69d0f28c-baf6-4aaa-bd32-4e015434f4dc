-- =====================================================
-- SUPABASE FIX: Create System User for AI Responses
-- =====================================================
-- Run this SQL in your Supabase SQL Editor to fix the 
-- "null value in column email" error

-- Create system user profile for AI responses
INSERT INTO profiles (id, email, name, role, is_active, created_at, updated_at)
VALUES (
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    'AI Assistant',
    'user',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    name = EXCLUDED.name,
    role = EXCLUDED.role,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Create index for better performance on sender_id if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);

-- Verify the system user was created
SELECT 
    id,
    email,
    name,
    role,
    is_active,
    created_at
FROM profiles 
WHERE id = '00000000-0000-0000-0000-000000000000';

-- Show a success message
SELECT 'System user created successfully! AI responses will now work properly.' as status;

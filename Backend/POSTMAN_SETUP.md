# Ava Chat System - Postman Setup Guide

## 📋 Quick Setup

### 1. Import Collection and Environment

1. **Import Collection**: 
   - Open Postman
   - Click "Import" → "Upload Files"
   - Select `Ava-Chat-System.postman_collection.json`

2. **Import Environment**:
   - Click "Import" → "Upload Files" 
   - Select `Ava-Chat-System.postman_environment.json`
   - Set as active environment in top-right dropdown

### 2. Start Your Server
```bash
cd Backend
npm run dev
```

### 3. Test Setup
Run the "Health Check" request to verify your server is running.

---

## 🔐 Authentication Flow

### Step 1: Register Users
1. **Register User**: Creates a regular user account
2. **Register Support Agent**: Creates a support team member account

### Step 2: Login
1. **Login User**: Automatically sets `access_token` and `refresh_token`
2. **Login Support Agent**: Automatically sets `support_access_token` and `support_refresh_token`

### Step 3: Test Authentication
1. **Get Profile**: Verify your authentication is working
2. **Update Profile**: Test profile modification

---

## 💬 Chat System Testing

### Basic Chat Flow
1. **Start New Conversation**: Creates a new conversation (sets `conversation_id`)
2. **Send Text Message**: Send a message and get AI response (may create Jira ticket)
3. **Send Voice Message**: Upload audio file for speech-to-text processing
4. **Get Conversation Messages**: View all messages in the conversation
5. **Get User Conversations**: List all user's conversations

### Advanced Chat Testing
- **Send Message to Specific Conversation**: Continue existing conversation
- **Get Current Conversation**: Get user's active conversation

---

## 🛠 Support Panel Testing

### Support Agent Workflow
1. **Login as Support Agent**: Use support credentials
2. **Get All Open Conversations**: View unassigned conversations
3. **Assign Conversation**: Assign conversation to yourself
4. **Send Support Message**: Respond to customer
5. **Get Assigned Conversations**: View your assigned conversations

### Jira Integration Testing
1. **Get Jira Ticket Details**: View ticket information
2. **Update Jira Ticket Status**: Change ticket status
3. **Add Comment to Jira Ticket**: Add internal comments
4. **Close Conversation**: Mark conversation as resolved

---

## 🧪 Sample Test Data

### User Registration
```json
{
  "email": "<EMAIL>",
  "password": "Password123!",
  "name": "John Doe",
  "role": "user",
  "avatar_url": "https://example.com/avatar.jpg"
}
```

### Support Agent Registration
```json
{
  "email": "<EMAIL>", 
  "password": "Password123!",
  "name": "Support Agent",
  "role": "support",
  "avatar_url": "https://example.com/support-avatar.jpg"
}
```

### Sample Messages

**General Inquiry:**
```json
{
  "message": "What are your business hours and how can I reach customer support?"
}
```

**Issue Report (Creates Jira Ticket):**
```json
{
  "message": "I'm experiencing a critical bug where the payment system is not processing transactions. I've tried multiple payment methods and browsers. This is blocking my business operations."
}
```

**Technical Question:**
```json
{
  "message": "How do I integrate your API with my existing system? I need documentation for the authentication endpoints."
}
```

---

## 🎯 Testing Workflow

### Complete End-to-End Test
1. **Setup Phase**:
   - Health check
   - Register user and support agent
   - Login both accounts

2. **User Chat Phase**:
   - Start conversation
   - Send text message
   - Send voice message (if available)
   - Send issue message (creates Jira ticket)

3. **Support Phase**:
   - Login as support agent
   - View open conversations
   - Assign conversation
   - Send support response
   - Update Jira ticket
   - Close conversation

4. **Verification Phase**:
   - Check conversation messages
   - Verify Jira ticket status
   - Test token refresh

---

## 🔧 Environment Variables

The environment includes these variables (automatically set by test scripts):

- `base_url`: Server URL (default: http://localhost:3000)
- `access_token`: User authentication token
- `refresh_token`: User refresh token  
- `support_access_token`: Support agent authentication token
- `support_refresh_token`: Support agent refresh token
- `user_id`: Current user ID
- `support_user_id`: Support agent user ID
- `conversation_id`: Current conversation ID
- `jira_ticket_id`: Current Jira ticket ID

---

## 📝 Response Examples

### Successful Login Response
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "user"
  },
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Chat Message Response
```json
{
  "success": true,
  "conversationId": "123e4567-e89b-12d3-a456-426614174000",
  "userMessage": {
    "id": "123e4567-e89b-12d3-a456-426614174001",
    "content": "Hello, I need help",
    "type": "text",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "aiResponse": {
    "id": "123e4567-e89b-12d3-a456-426614174002", 
    "content": "Hello! I'd be happy to help you.",
    "timestamp": "2024-01-15T10:30:01Z"
  },
  "jiraTicketId": "PROJ-123"
}
```

---

## 🚨 Troubleshooting

### Common Issues

1. **401 Unauthorized**: 
   - Check if you're logged in
   - Verify token is set in environment
   - Try refreshing token

2. **403 Forbidden**:
   - Check user role (support endpoints require support role)
   - Verify you're using correct token

3. **File Upload Issues**:
   - Ensure file is under 10MB
   - Use supported audio formats (WebM, MP3, WAV, FLAC, OGG)
   - Check file is selected in form-data

4. **Rate Limiting**:
   - Wait for rate limit to reset
   - Check console for rate limit messages

### Debug Tips
- Check Postman console for detailed error messages
- Verify environment variables are set correctly
- Use "Get Profile" to test authentication
- Check server logs for detailed error information

---

## 📚 Additional Resources

- [API Documentation](./POSTMAN_COLLECTION.md)
- [Security Documentation](./SECURITY.md)
- [Development Setup](./README.md)

This setup provides comprehensive testing coverage for all Ava Chat System endpoints with proper authentication handling and environment management.

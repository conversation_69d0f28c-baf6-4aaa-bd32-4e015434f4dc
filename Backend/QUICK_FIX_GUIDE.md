# 🚀 Quick Fix Guide - Ava Chat System

## ❌ Current Issue
**Error**: `"null value in column \"email\" of relation \"profiles\" violates not-null constraint"`

**Cause**: The system user profile for AI responses doesn't have an email address, but the profiles table requires one.

---

## ✅ Solution Steps

### Step 1: Fix Database (Run in Supabase SQL Editor)

Copy and paste this SQL into your **Supabase SQL Editor** and run it:

```sql
-- Create system user profile for AI responses
INSERT INTO profiles (id, email, name, role, is_active, created_at, updated_at)
VALUES (
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    'AI Assistant',
    'user',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    name = EXCLUDED.name,
    role = EXCLUDED.role,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);

-- Verify the fix
SELECT 'System user created successfully!' as status,
       id, email, name, role 
FROM profiles 
WHERE id = '00000000-0000-0000-0000-000000000000';
```

### Step 2: Restart Your Server

```bash
# Stop current server (Ctrl+C if running)
# Then restart:
npm run dev
```

### Step 3: Test the Fix

Send a chat message through your API or Postman - the AI response should now work without the email error.

---

## 📋 Postman Collection Setup

### Import Files
1. **Collection**: Import `Ava-Chat-System.postman_collection.json`
2. **Environment**: Import `Ava-Chat-System.postman_environment.json`
3. **Set Environment**: Select "Ava Chat System - Local Development" as active environment

### Test Workflow
1. **Health Check**: `GET /health`
2. **Register User**: `POST /api/auth/register`
3. **Login User**: `POST /api/auth/login` (auto-sets tokens)
4. **Send Message**: `POST /api/chat/send-message`
5. **Register Support**: `POST /api/auth/register` (role: support)
6. **Login Support**: `POST /api/auth/login` (auto-sets support tokens)
7. **View Conversations**: `GET /api/support/conversations`

---

## 🧪 Quick API Test

Run this to test your endpoints:

```bash
# Test health endpoint
curl http://localhost:3000/health

# Test registration
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123!",
    "name": "Test User",
    "role": "user"
  }'

# Test login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Password123!"
  }'
```

---

## 📁 Files Created/Updated

### ✅ Fixed Files
- `src/middleware/security.ts` - Fixed ExpressSlowDownWarning
- `db/fix-system-user.sql` - Updated with email field
- `migrations/complete_database_schema.sql` - Added system user creation

### 📦 New Files
- `Ava-Chat-System.postman_collection.json` - Complete API collection
- `Ava-Chat-System.postman_environment.json` - Environment variables
- `POSTMAN_SETUP.md` - Detailed setup guide
- `SUPABASE_FIX.sql` - Quick database fix
- `test-postman-endpoints.sh` - API testing script
- `QUICK_FIX_GUIDE.md` - This guide

---

## 🎯 Expected Results After Fix

### ✅ Before Fix (Issues)
- ❌ ExpressSlowDownWarning on server start
- ❌ "null value in column email" error on AI responses
- ❌ No comprehensive Postman collection

### ✅ After Fix (Working)
- ✅ Clean server startup (no warnings)
- ✅ AI responses work properly
- ✅ Complete Postman collection with all endpoints
- ✅ Automatic token management in Postman
- ✅ Full testing workflow

---

## 🔧 Troubleshooting

### If SQL Fix Doesn't Work
1. Check if you're connected to the right Supabase project
2. Verify the profiles table exists
3. Check if the system user already exists:
   ```sql
   SELECT * FROM profiles WHERE id = '00000000-0000-0000-0000-000000000000';
   ```

### If Postman Collection Issues
1. Ensure environment is selected
2. Check base_url is set to `http://localhost:3000`
3. Run login requests first to set tokens
4. Check server is running on port 3000

### If API Still Shows Errors
1. Check server logs for detailed error messages
2. Verify all environment variables are set
3. Restart the server after database changes
4. Test with simple curl commands first

---

## 📞 Support

If you encounter any issues:
1. Check the server console for detailed error messages
2. Verify the SQL was executed successfully in Supabase
3. Test individual endpoints with curl before using Postman
4. Ensure all environment variables are properly configured

**The fix should resolve the email constraint error and provide a complete testing setup!** 🎉

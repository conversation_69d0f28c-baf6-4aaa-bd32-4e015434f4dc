{"info": {"_postman_id": "ava-chat-system-collection", "name": "Ava Chat System API", "description": "Complete API collection for Ava Chat System with authentication, chat, and support endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.accessToken) {", "        pm.environment.set('access_token', response.accessToken);", "    }", "    if (response.refreshToken) {", "        pm.environment.set('refresh_token', response.refreshToken);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\",\n  \"name\": \"<PERSON>\",\n  \"role\": \"user\",\n  \"avatar_url\": \"https://example.com/avatar.jpg\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}}, {"name": "Register Support Agent", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.accessToken) {", "        pm.environment.set('support_access_token', response.accessToken);", "    }", "    if (response.refreshToken) {", "        pm.environment.set('support_refresh_token', response.refreshToken);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\",\n  \"name\": \"Support Agent\",\n  \"role\": \"support\",\n  \"avatar_url\": \"https://example.com/support-avatar.jpg\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.accessToken) {", "        pm.environment.set('access_token', response.accessToken);", "    }", "    if (response.refreshToken) {", "        pm.environment.set('refresh_token', response.refreshToken);", "    }", "    if (response.user && response.user.id) {", "        pm.environment.set('user_id', response.user.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "Login Support Agent", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.accessToken) {", "        pm.environment.set('support_access_token', response.accessToken);", "    }", "    if (response.refreshToken) {", "        pm.environment.set('support_refresh_token', response.refreshToken);", "    }", "    if (response.user && response.user.id) {", "        pm.environment.set('support_user_id', response.user.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.accessToken) {", "        pm.environment.set('access_token', response.accessToken);", "    }", "    if (response.refreshToken) {", "        pm.environment.set('refresh_token', response.refreshToken);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "auth", "refresh"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/auth/logout", "host": ["{{base_url}}"], "path": ["api", "auth", "logout"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/auth/profile", "host": ["{{base_url}}"], "path": ["api", "auth", "profile"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Name\",\n  \"avatar_url\": \"https://example.com/new-avatar.jpg\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/profile", "host": ["{{base_url}}"], "path": ["api", "auth", "profile"]}}}]}, {"name": "💬 Chat System", "item": [{"name": "Start New Conversation", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.conversationId) {", "        pm.environment.set('conversation_id', response.conversationId);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/chat/start-conversation", "host": ["{{base_url}}"], "path": ["api", "chat", "start-conversation"]}}}, {"name": "Send Text Message", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.conversationId) {", "        pm.environment.set('conversation_id', response.conversationId);", "    }", "    if (response.jiraTicketId) {", "        pm.environment.set('jira_ticket_id', response.jiraTicketId);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"Hello, I need help with my account settings. I'm experiencing issues with login and password reset functionality.\"\n}"}, "url": {"raw": "{{base_url}}/api/chat/send-message", "host": ["{{base_url}}"], "path": ["api", "chat", "send-message"]}}}, {"name": "Send Voice Message", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.conversationId) {", "        pm.environment.set('conversation_id', response.conversationId);", "    }", "    if (response.jiraTicketId) {", "        pm.environment.set('jira_ticket_id', response.jiraTicketId);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "audio", "type": "file", "src": [], "description": "Upload audio file (WebM, MP3, WAV, FLAC, OGG - max 10MB)"}]}, "url": {"raw": "{{base_url}}/api/chat/send-message", "host": ["{{base_url}}"], "path": ["api", "chat", "send-message"]}}}, {"name": "Send Message to Specific Conversation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"I have an additional question about the previous issue.\"\n}"}, "url": {"raw": "{{base_url}}/api/chat/conversations/{{conversation_id}}/send-message", "host": ["{{base_url}}"], "path": ["api", "chat", "conversations", "{{conversation_id}}", "send-message"]}}}, {"name": "Get Current Conversation", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/chat/current-conversation", "host": ["{{base_url}}"], "path": ["api", "chat", "current-conversation"]}}}, {"name": "Get Conversation Messages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/chat/conversation/{{conversation_id}}/messages?page=1&limit=50", "host": ["{{base_url}}"], "path": ["api", "chat", "conversation", "{{conversation_id}}", "messages"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}]}}}, {"name": "Get User Conversations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/chat/conversations?page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "chat", "conversations"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}]}, {"name": "🛠 Support Panel", "item": [{"name": "Get All Open Conversations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{support_access_token}}"}], "url": {"raw": "{{base_url}}/api/support/conversations?status=open&page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "support", "conversations"], "query": [{"key": "status", "value": "open"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Assigned Conversations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{support_access_token}}"}], "url": {"raw": "{{base_url}}/api/support/conversations?status=assigned&page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "support", "conversations"], "query": [{"key": "status", "value": "assigned"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Assign Conversation to Support Agent", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{support_access_token}}"}], "url": {"raw": "{{base_url}}/api/support/conversations/{{conversation_id}}/assign", "host": ["{{base_url}}"], "path": ["api", "support", "conversations", "{{conversation_id}}", "assign"]}}}, {"name": "Send Support Message", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{support_access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"Hello! I'm here to help you with your account issue. Can you please provide more details about the specific problem you're experiencing?\"\n}"}, "url": {"raw": "{{base_url}}/api/support/conversations/{{conversation_id}}/messages", "host": ["{{base_url}}"], "path": ["api", "support", "conversations", "{{conversation_id}}", "messages"]}}}, {"name": "Close Conversation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{support_access_token}}"}], "url": {"raw": "{{base_url}}/api/support/conversations/{{conversation_id}}/close", "host": ["{{base_url}}"], "path": ["api", "support", "conversations", "{{conversation_id}}", "close"]}}}, {"name": "Get Jira Ticket Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{support_access_token}}"}], "url": {"raw": "{{base_url}}/api/support/jira/ticket/{{jira_ticket_id}}", "host": ["{{base_url}}"], "path": ["api", "support", "jira", "ticket", "{{jira_ticket_id}}"]}}}, {"name": "Update Jira Ticket Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{support_access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"In Progress\"\n}"}, "url": {"raw": "{{base_url}}/api/support/jira/ticket/{{jira_ticket_id}}/status", "host": ["{{base_url}}"], "path": ["api", "support", "jira", "ticket", "{{jira_ticket_id}}", "status"]}}}, {"name": "Add Comment to <PERSON><PERSON> Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{support_access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"comment\": \"Customer issue has been investigated. Providing solution and following up.\"\n}"}, "url": {"raw": "{{base_url}}/api/support/jira/ticket/{{jira_ticket_id}}/comment", "host": ["{{base_url}}"], "path": ["api", "support", "jira", "ticket", "{{jira_ticket_id}}", "comment"]}}}]}, {"name": "🧪 Test Endpoints", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}, {"name": "API Test Endpoint", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/test", "host": ["{{base_url}}"], "path": ["api", "test"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "// Add any global setup here"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "// Add any global tests here"]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}]}